.
├── bin
│   └── console -> ../src/console.php
├── build
│   ├── phpunit
│   │   ├── integration.cache
│   │   │   └── test-results
│   │   ├── unit.cache
│   │   │   └── test-results
│   │   └── junit.xml
│   ├── phpcs.cache
│   ├── phplint.log
│   └── phpstan.junit.xml
├── config
│   ├── dev.php
│   ├── phpunit.php
│   └── prod.php
├── database
│   └── database.sqlite
├── docker
│   ├── development
│   │   ├── nginx
│   │   │   ├── certificate
│   │   │   ├── certificate_key
│   │   │   ├── nginx.conf.template
│   │   │   └── readme
│   │   └── php
│   │       ├── files
│   │       │   ├── etc
│   │       │   │   ├── opt
│   │       │   │   │   └── remi
│   │       │   │   │       └── php84
│   │       │   │   │           └── php.d
│   │       │   │   │               └── 99-development.ini
│   │       │   │   └── supervisord.conf
│   │       │   └── home
│   │       │       └── php
│   │       │           ├── .bashrc
│   │       │           ├── .sharedrc
│   │       │           └── .zshrc
│   │       └── Dockerfile
│   └── production
│       └── php
│           ├── files
│           │   └── etc
│           │       └── opt
│           │           └── remi
│           │               └── php84
│           │                   ├── php-fpm.conf
│           │                   └── php.ini
│           └── Dockerfile
├── frontend
│   ├── src
│   │   ├── lib
│   │   │   ├── assets
│   │   │   │   └── favicon.svg
│   │   │   └── index.ts
│   │   ├── routes
│   │   │   ├── +layout.svelte
│   │   │   └── +page.svelte
│   │   ├── app.css
│   │   ├── app.d.ts
│   │   ├── app.html
│   │   ├── App.svelte
│   │   ├── ArticleForm.svelte
│   │   └── ArticleList.svelte
│   ├── static
│   │   └── robots.txt
│   ├── .svelte-kit
│   │   ├── generated
│   │   │   ├── client
│   │   │   │   ├── nodes
│   │   │   │   │   ├── 0.js
│   │   │   │   │   ├── 1.js
│   │   │   │   │   └── 2.js
│   │   │   │   ├── app.js
│   │   │   │   └── matchers.js
│   │   │   ├── client-optimized
│   │   │   │   ├── nodes
│   │   │   │   │   ├── 0.js
│   │   │   │   │   ├── 1.js
│   │   │   │   │   └── 2.js
│   │   │   │   ├── app.js
│   │   │   │   └── matchers.js
│   │   │   ├── server
│   │   │   │   └── internal.js
│   │   │   ├── root.js
│   │   │   └── root.svelte
│   │   ├── output
│   │   │   ├── client
│   │   │   │   ├── _app
│   │   │   │   │   ├── immutable
│   │   │   │   │   │   ├── assets
│   │   │   │   │   │   │   └── 0.CIFRERqu.css
│   │   │   │   │   │   ├── chunks
│   │   │   │   │   │   │   ├── a2_yskie.js
│   │   │   │   │   │   │   ├── Bar4knpK.js
│   │   │   │   │   │   │   ├── CBR5S-n2.js
│   │   │   │   │   │   │   ├── DsnmJJEf.js
│   │   │   │   │   │   │   ├── JFgEwnbB.js
│   │   │   │   │   │   │   └── twyD1gSU.js
│   │   │   │   │   │   ├── entry
│   │   │   │   │   │   │   ├── app.DpltqXE1.js
│   │   │   │   │   │   │   └── start.D_tbrfGi.js
│   │   │   │   │   │   └── nodes
│   │   │   │   │   │       ├── 0.CDXPFCdE.js
│   │   │   │   │   │       ├── 1.CnFo-KcW.js
│   │   │   │   │   │       └── 2.CBhFRLQx.js
│   │   │   │   │   └── version.json
│   │   │   │   ├── .vite
│   │   │   │   │   └── manifest.json
│   │   │   │   └── robots.txt
│   │   │   ├── prerendered
│   │   │   │   └── dependencies
│   │   │   │       └── _app
│   │   │   │           └── env.js
│   │   │   └── server
│   │   │       ├── _app
│   │   │       │   └── immutable
│   │   │       │       └── assets
│   │   │       │           └── _layout.BLNKXymd.css
│   │   │       ├── chunks
│   │   │       │   ├── context.js
│   │   │       │   ├── environment.js
│   │   │       │   ├── equality.js
│   │   │       │   ├── escaping.js
│   │   │       │   ├── exports.js
│   │   │       │   ├── index.js
│   │   │       │   ├── internal.js
│   │   │       │   ├── shared.js
│   │   │       │   └── utils.js
│   │   │       ├── entries
│   │   │       │   ├── fallbacks
│   │   │       │   │   └── error.svelte.js
│   │   │       │   └── pages
│   │   │       │       ├── _layout.svelte.js
│   │   │       │       └── _page.svelte.js
│   │   │       ├── nodes
│   │   │       │   ├── 0.js
│   │   │       │   ├── 1.js
│   │   │       │   └── 2.js
│   │   │       ├── stylesheets
│   │   │       ├── .vite
│   │   │       │   └── manifest.json
│   │   │       ├── index.js
│   │   │       ├── internal.js
│   │   │       ├── manifest-full.js
│   │   │       ├── manifest.js
│   │   │       └── remote-entry.js
│   │   ├── types
│   │   │   ├── src
│   │   │   │   └── routes
│   │   │   │       └── $types.d.ts
│   │   │   └── route_meta_data.json
│   │   ├── ambient.d.ts
│   │   ├── non-ambient.d.ts
│   │   └── tsconfig.json
│   ├── .gitignore
│   ├── .npmrc
│   ├── package.json
│   ├── pnpm-lock.yaml
│   ├── README.md
│   ├── svelte.config.js
│   ├── tsconfig.json
│   └── vite.config.ts
├── .github
│   └── workflows
│       └── ci.yml
├── src
│   ├── Collection
│   │   ├── AbstractCollection.php
│   │   ├── ArticleCollection.php
│   │   ├── CollectionInterface.php
│   │   └── PetCollection.php
│   ├── Dto
│   │   ├── Collection
│   │   │   ├── ArticleCollectionFilters.php
│   │   │   ├── ArticleCollectionRequest.php
│   │   │   ├── ArticleCollectionResponse.php
│   │   │   ├── ArticleCollectionSort.php
│   │   │   ├── CollectionRequestInterface.php
│   │   │   ├── PetCollectionFilters.php
│   │   │   ├── PetCollectionRequest.php
│   │   │   ├── PetCollectionResponse.php
│   │   │   └── PetCollectionSort.php
│   │   └── Model
│   │       ├── ArticleRequest.php
│   │       ├── ArticleResponse.php
│   │       ├── CategoryRequest.php
│   │       ├── CategoryResponse.php
│   │       ├── ModelRequestInterface.php
│   │       ├── PetRequest.php
│   │       ├── PetResponse.php
│   │       ├── VaccinationRequest.php
│   │       └── VaccinationResponse.php
│   ├── Middleware
│   │   └── ApiExceptionMiddleware.php
│   ├── Model
│   │   ├── Article.php
│   │   ├── Category.php
│   │   ├── ModelInterface.php
│   │   ├── Pet.php
│   │   └── Vaccination.php
│   ├── Orm
│   │   ├── ArticleMapping.php
│   │   ├── CategoryMapping.php
│   │   ├── PetMapping.php
│   │   └── VaccinationMapping.php
│   ├── Parsing
│   │   ├── ArticleParsing.php
│   │   ├── ParsingInterface.php
│   │   └── PetParsing.php
│   ├── Repository
│   │   ├── ArticleRepository.php
│   │   ├── PetRepository.php
│   │   └── RepositoryInterface.php
│   ├── RequestHandler
│   │   ├── Api
│   │   │   └── Crud
│   │   │       ├── CreateRequestHandler.php
│   │   │       ├── DeleteRequestHandler.php
│   │   │       ├── ListRequestHandler.php
│   │   │       ├── ReadRequestHandler.php
│   │   │       └── UpdateRequestHandler.php
│   │   ├── OpenapiRequestHandler.php
│   │   └── PingRequestHandler.php
│   ├── ServiceFactory
│   │   ├── Command
│   │   │   └── CommandsFactory.php
│   │   ├── DecodeEncode
│   │   │   ├── TypeDecodersFactory.php
│   │   │   └── TypeEncodersFactory.php
│   │   ├── Framework
│   │   │   ├── ExceptionMiddlewareFactory.php
│   │   │   ├── MiddlewaresFactory.php
│   │   │   ├── RouteMatcherFactory.php
│   │   │   ├── RouteMatcherMiddlewareFactory.php
│   │   │   ├── RoutesByNameFactory.php
│   │   │   └── UrlGeneratorFactory.php
│   │   ├── Http
│   │   │   ├── ResponseFactoryFactory.php
│   │   │   └── StreamFactoryFactory.php
│   │   ├── Logger
│   │   │   └── LoggerFactory.php
│   │   ├── Middleware
│   │   │   └── ApiExceptionMiddlewareFactory.php
│   │   ├── Negotiation
│   │   │   ├── AcceptNegotiatorSupportedMediaTypesFactory.php
│   │   │   └── ContentTypeNegotiatorSupportedMediaTypesFactory.php
│   │   ├── Parsing
│   │   │   ├── ArticleParsingFactory.php
│   │   │   ├── ParserFactory.php
│   │   │   └── PetParsingFactory.php
│   │   ├── Repository
│   │   │   ├── ArticleRepositoryFactory.php
│   │   │   └── PetRepositoryFactory.php
│   │   └── RequestHandler
│   │       ├── Api
│   │       │   └── Crud
│   │       │       ├── ArticleCreateRequestHandlerFactory.php
│   │       │       ├── ArticleDeleteRequestHandlerFactory.php
│   │       │       ├── ArticleListRequestHandlerFactory.php
│   │       │       ├── ArticleReadRequestHandlerFactory.php
│   │       │       ├── ArticleUpdateRequestHandlerFactory.php
│   │       │       ├── PetCreateRequestHandlerFactory.php
│   │       │       ├── PetDeleteRequestHandlerFactory.php
│   │       │       ├── PetListRequestHandlerFactory.php
│   │       │       ├── PetReadRequestHandlerFactory.php
│   │       │       └── PetUpdateRequestHandlerFactory.php
│   │       ├── OpenapiRequestHandlerFactory.php
│   │       └── PingRequestHandlerFactory.php
│   ├── console.php
│   ├── container.php
│   ├── Seeder.php
│   └── web.php
├── svelte 
│   ├── frontend
│   │   └── src
│   │       ├── components
│   │       │   ├── islands
│   │       │   │   ├── AddToCart.svelte
│   │       │   │   └── Alert.svelte
│   │       │   ├── layout
│   │       │   │   ├── Header.svelte
│   │       │   │   ├── Hero.svelte
│   │       │   │   └── TailwindHero.svelteZAL
│   │       │   ├── sections
│   │       │   ├── ui
│   │       │   │   ├── ArticleCard.svelte
│   │       │   │   ├── ArticleDetail.svelte
│   │       │   │   ├── Footer.svelte
│   │       │   │   ├── Header.svelte
│   │       │   │   ├── Hero.svelte
│   │       │   │   ├── Nav.svelte
│   │       │   │   ├── SearchModal.svelte
│   │       │   │   └── TailwindHero.svelte
│   │       │   └── index.ts
│   │       ├── core
│   │       │   ├── boot.ts
│   │       │   ├── ComponentRegistry.ts
│   │       │   └── islands.ts
│   │       ├── pages
│   │       │   ├── App.svelte
│   │       │   ├── CoolIndex.svelte
│   │       │   └── Welcome.svelte
│   │       ├── stores
│   │       │   ├── app.store.ts
│   │       │   ├── cart.store.ts
│   │       │   ├── index.ts
│   │       │   ├── ui.store.ts
│   │       │   └── user.store.ts
│   │       ├── styles
│   │       │   ├── app.css
│   │       │   └── tailwind.css
│   │       ├── types
│   │       │   ├── app.d.ts
│   │       │   ├── global.d.ts
│   │       │   ├── htmx.d.ts
│   │       │   └── vite-env.d.ts
│   │       ├── utils
│   │       │   ├── api.ts
│   │       │   ├── constants.ts
│   │       │   ├── formatters.ts
│   │       │   ├── htmx.utils.ts
│   │       │   ├── index.ts
│   │       │   └── validation.ts
│   │       └── app.ts
│   ├── src
│   │   └── App
│   │       ├── Handler
│   │       │   ├── Api
│   │       │   │   ├── PingHandlerFactory.php
│   │       │   │   └── PingHandler.php
│   │       │   ├── Htmx
│   │       │   │   ├── ContentHandlerFactory.php
│   │       │   │   ├── ContentHandler.php
│   │       │   │   ├── PartialApiFactory.php
│   │       │   │   └── PartialApi.php
│   │       │   ├── Web
│   │       │   │   ├── ComponentDemoHandlerFactory.php
│   │       │   │   ├── ComponentDemoHandler.php
│   │       │   │   ├── CoolIndexHandlerFactory.php
│   │       │   │   ├── CoolIndexHandler.php
│   │       │   │   ├── HeroHandlerFactory.php
│   │       │   │   ├── HeroHandler.php
│   │       │   │   ├── HomePageHandlerFactory.php
│   │       │   │   ├── HomePageHandler.php
│   │       │   │   ├── NotFoundHandlerFactory.php
│   │       │   │   ├── NotFoundHandler.php
│   │       │   │   ├── TestCsrfHandlerFactory.php
│   │       │   │   ├── TestCsrfHandler.php
│   │       │   │   ├── TestFrontendHandlerFactory.php
│   │       │   │   └── TestFrontendHandler.php
│   │       │   └── AbstractHandler.php
│   │       ├── Middleware
│   │       │   ├── CacheMiddleware.php
│   │       │   ├── CspMiddleware.php
│   │       │   └── SvelteDataMiddleware.php
│   │       ├── Repository
│   │       │   ├── ProductRepositoryInterface.php
│   │       │   └── ProductRepository.php
│   │       ├── Service
│   │       │   ├── ResponseStrategy
│   │       │   │   ├── ApiResponseStrategy.php
│   │       │   │   ├── FragmentResponseStrategy.php
│   │       │   │   ├── PageResponseStrategy.php
│   │       │   │   ├── ResponseStrategyInterface.php
│   │       │   │   └── ResponseStrategySelector.php
│   │       │   ├── ProductServiceFactory.php
│   │       │   └── ProductService.php
│   │       ├── View
│   │       │   ├── Helper
│   │       │   │   └── CsrfHelper.php
│   │       │   └── Twig
│   │       │       ├── CsrfExtension.php
│   │       │       └── SvelteComponentExtension.php
│   │       └── ConfigProvider.php
│   ├── templates
│   │   ├── app
│   │   │   ├── component-demo.html.twig
│   │   │   ├── cool-index.html.twig
│   │   │   ├── hero.html.twig
│   │   │   ├── home-page.html.twig
│   │   │   ├── products.html.twig
│   │   │   └── test-frontend.html.twig
│   │   ├── error
│   │   │   ├── 404.html.twig
│   │   │   └── error.html.twig
│   │   ├── layout
│   │   │   └── default.html.twig
│   │   └── partials
│   │       └── components.twig
│   ├── composer.json
│   ├── eslint.config.js
│   ├── package.json
│   ├── postcss.config.js
│   ├── svelte.config.js
│   ├── tailwind.config.js
│   ├── tsconfig.eslint.json
│   ├── tsconfig.json
│   └── vite.config.js
├── tests
│   ├── Helper
│   │   └── AssertHelper.php
│   ├── Integration
│   │   ├── AbstractIntegrationTestCase.php
│   │   ├── CorsControllerTest.php
│   │   ├── OpenapiRequestHandlerTest.php
│   │   ├── PetCrudRequestHandlerTest.php
│   │   └── PingRequestHandlerTest.php
│   ├── Unit
│   │   ├── Collection
│   │   │   ├── CollectionTest.php
│   │   │   └── PetCollectionTest.php
│   │   ├── Dto
│   │   │   ├── Collection
│   │   │   │   ├── PetCollectionRequestTest.php
│   │   │   │   └── PetCollectionResponseTest.php
│   │   │   └── Model
│   │   │       ├── PetRequestTest.php
│   │   │       └── PetResponseTest.php
│   │   ├── Middleware
│   │   │   └── ApiExceptionMiddlewareTest.php
│   │   ├── Model
│   │   │   ├── PetTest.php
│   │   │   └── VaccinationTest.php
│   │   ├── Orm
│   │   │   ├── PetMappingTest.php
│   │   │   └── VaccinationMappingTest.php
│   │   ├── Parsing
│   │   │   └── PetParsingTest.php
│   │   ├── Repository
│   │   │   └── PetRepositoryTest.php
│   │   ├── RequestHandler
│   │   │   ├── Api
│   │   │   │   └── Crud
│   │   │   │       ├── CreateRequestHandlerTest.php
│   │   │   │       ├── DeleteRequestHandlerTest.php
│   │   │   │       ├── ListRequestHandlerTest.php
│   │   │   │       ├── ReadRequestHandlerTest.php
│   │   │   │       └── UpdateRequestHandlerTest.php
│   │   │   ├── OpenapiRequestHandlerTest.php
│   │   │   └── PingRequestHandlerTest.php
│   │   └── ServiceFactory
│   │       ├── Command
│   │       │   └── CommandsFactoryTest.php
│   │       ├── DecodeEncode
│   │       │   ├── TypeDecodersFactoryTest.php
│   │       │   └── TypeEncodersFactoryTest.php
│   │       ├── Framework
│   │       │   ├── ExceptionMiddlewareFactoryTest.php
│   │       │   ├── MiddlewaresFactoryTest.php
│   │       │   ├── RouteMatcherFactoryTest.php
│   │       │   ├── RouteMatcherMiddlewareFactoryTest.php
│   │       │   ├── RoutesByNameFactoryTest.php
│   │       │   └── UrlGeneratorFactoryTest.php
│   │       ├── Http
│   │       │   ├── ResponseFactoryFactoryTest.php
│   │       │   └── StreamFactoryFactoryTest.php
│   │       ├── Logger
│   │       │   └── LoggerFactoryTest.php
│   │       ├── Middleware
│   │       │   └── ApiExceptionMiddlewareFactoryTest.php
│   │       ├── Negotiation
│   │       │   ├── AcceptNegotiatorSupportedMediaTypesFactoryTest.php
│   │       │   └── ContentTypeNegotiatorSupportedMediaTypesFactoryTest.php
│   │       ├── Parsing
│   │       │   ├── ParserFactoryTest.php
│   │       │   └── PetParsingFactoryTest.php
│   │       ├── Repository
│   │       │   └── PetRepositoryFactoryTest.php
│   │       └── RequestHandler
│   │           ├── Api
│   │           │   └── Crud
│   │           │       ├── PetCreateRequestHandlerFactoryTest.php
│   │           │       ├── PetDeleteRequestHandlerFactoryTest.php
│   │           │       ├── PetListRequestHandlerFactoryTest.php
│   │           │       ├── PetReadRequestHandlerFactoryTest.php
│   │           │       └── PetUpdateRequestHandlerFactoryTest.php
│   │           ├── OpenapiRequestHandlerFactoryTest.php
│   │           └── PingRequestHandlerFactoryTest.php
│   └── PhpServerExtension.php
├── var
│   ├── cache
│   │   ├── dev
│   │   └── phpunit
│   ├── log
│   │   └── dev.log
│   └── articlestore.db
├── composer.json
├── composer.lock
├── DEPLOYMENT.md
├── directory_tree.md
├── docker-compose.ci.yml
├── docker-compose.yml
├── .dockerignore
├── .gitignore
├── infection.json
├── LICENSE
├── openapi.yml
├── .phpactor.json
├── .php-cs-fixer.php
├── phpstan.neon
├── phpunit.integration.xml
├── phpunit.xml
├── README.md
├── sonar-project.properties
└── TODO.md

160 directories, 348 files
