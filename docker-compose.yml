services:
  php:
    container_name: petstore-php
    hostname: petstore-php
    build:
      dockerfile: ./docker/development/php/Dockerfile
      context: ./
      args:
        USER_ID: ${USER_ID:-1000}
        GROUP_ID: ${GROUP_ID:-1000}
    environment:
      APP_ENV: dev
      SSH_AUTH_SOCK: /ssh-agent
    volumes:
      - ${PWD}:/app
      - ~/.bash_docker:/home/<USER>/.bash_docker
      - ~/.bash_history:/home/<USER>/.bash_history
      - ~/.gitconfig:/home/<USER>/.gitconfig
      - ~/.gitignore:/home/<USER>/.gitignore
      - ~/.zsh_docker:/home/<USER>/.zsh_docker
      - ~/.zsh_history:/home/<USER>/.zsh_history
      - $SSH_AUTH_SOCK:/ssh-agent
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - '9003:9003'
  swagger-ui:
    container_name: petstore-swagger-ui
    hostname: petstore-swagger-ui
    image: swaggerapi/swagger-ui
    environment:
      BASE_URL: /swagger
      URLS: '[ { url: "/openapi" } ]'
  nginx:
    container_name: petstore-nginx
    hostname: petstore-nginx
    image: nginx
    environment:
      SERVER_PORT: '443'
      PHP_FPM_SERVER_HOST: 'php'
      PHP_FPM_SERVER_PORT: '9000'
      SWAGGER_SERVER_HOST: 'swagger-ui'
      SWAGGER_SERVER_PORT: '8080'
    ports:
      - '443:443'
    volumes:
      - ./docker/development/nginx:/etc/nginx/templates
      - ${PWD}/public:/app/public:ro
    depends_on:
      - php
      - swagger-ui
