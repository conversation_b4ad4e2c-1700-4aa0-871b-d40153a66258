<?php

declare(strict_types=1);

use App\Middleware\ApiExceptionMiddleware;
use App\Model\Article;
use App\Model\Category;
use App\Model\Pet;
use App\Model\Vaccination;
use App\Orm\ArticleMapping;
use App\Orm\CategoryMapping;
use App\Orm\PetMapping;
use App\Orm\VaccinationMapping;
use App\Parsing\PetParsing;
use App\Repository\PetRepository;
use App\RequestHandler\Api\Crud\CreateRequestHandler;
use App\RequestHandler\Api\Crud\DeleteRequestHandler;
use App\RequestHandler\Api\Crud\ListRequestHandler;
use App\RequestHandler\Api\Crud\ReadRequestHandler;
use App\RequestHandler\Api\Crud\UpdateRequestHandler;
use App\RequestHandler\OpenapiRequestHandler;
use App\RequestHandler\PingRequestHandler;
use App\ServiceFactory\Command\CommandsFactory;
use App\ServiceFactory\DecodeEncode\TypeDecodersFactory;
use App\ServiceFactory\DecodeEncode\TypeEncodersFactory;
use App\ServiceFactory\Framework\ExceptionMiddlewareFactory;
use App\ServiceFactory\Framework\MiddlewaresFactory;
use App\ServiceFactory\Framework\RouteMatcherFactory;
use App\ServiceFactory\Framework\RouteMatcherMiddlewareFactory;
use App\ServiceFactory\Framework\RoutesByNameFactory;
use App\ServiceFactory\Framework\UrlGeneratorFactory;
use App\ServiceFactory\Http\ResponseFactoryFactory;
use App\ServiceFactory\Http\StreamFactoryFactory;
use App\ServiceFactory\Logger\LoggerFactory;
use App\ServiceFactory\Middleware\ApiExceptionMiddlewareFactory;
use App\ServiceFactory\Negotiation\AcceptNegotiatorSupportedMediaTypesFactory;
use App\ServiceFactory\Negotiation\ContentTypeNegotiatorSupportedMediaTypesFactory;
use App\ServiceFactory\Parsing\ParserFactory;
use App\ServiceFactory\Parsing\PetParsingFactory;
use App\ServiceFactory\Parsing\ArticleParsingFactory;
use App\ServiceFactory\Repository\PetRepositoryFactory;
use App\ServiceFactory\Repository\ArticleRepositoryFactory;
use App\ServiceFactory\RequestHandler\Api\Crud\PetCreateRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\Api\Crud\PetDeleteRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\Api\Crud\PetListRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\Api\Crud\PetReadRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\Api\Crud\PetUpdateRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\Api\Crud\ArticleCreateRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\Api\Crud\ArticleDeleteRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\Api\Crud\ArticleListRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\Api\Crud\ArticleReadRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\Api\Crud\ArticleUpdateRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\OpenapiRequestHandlerFactory;
use App\ServiceFactory\RequestHandler\PingRequestHandlerFactory;
use Chubbyphp\Cors\CorsMiddleware;
use Chubbyphp\Cors\ServiceFactory\CorsMiddlewareFactory;
use Chubbyphp\DecodeEncode\Decoder\DecoderInterface;
use Chubbyphp\DecodeEncode\Decoder\TypeDecoderInterface;
use Chubbyphp\DecodeEncode\Encoder\EncoderInterface;
use Chubbyphp\DecodeEncode\Encoder\TypeEncoderInterface;
use Chubbyphp\DecodeEncode\ServiceFactory\DecoderFactory;
use Chubbyphp\DecodeEncode\ServiceFactory\EncoderFactory;
use Chubbyphp\Framework\Middleware\ExceptionMiddleware;
use Chubbyphp\Framework\Middleware\RouteMatcherMiddleware;
use Chubbyphp\Framework\Router\RouteMatcherInterface;
use Chubbyphp\Framework\Router\RoutesByNameInterface;
use Chubbyphp\Framework\Router\UrlGeneratorInterface;
use Chubbyphp\Laminas\Config\Doctrine\ServiceFactory\Common\Cache\ApcuAdapterFactory;
use Chubbyphp\Laminas\Config\Doctrine\ServiceFactory\DBAL\ConnectionFactory;
use Chubbyphp\Laminas\Config\Doctrine\ServiceFactory\DBAL\Tools\Console\ContainerConnectionProviderFactory;
use Chubbyphp\Laminas\Config\Doctrine\ServiceFactory\ORM\EntityManagerFactory;
use Chubbyphp\Laminas\Config\Doctrine\ServiceFactory\ORM\Tools\Console\ContainerEntityManagerProviderFactory;
use Chubbyphp\Laminas\Config\Doctrine\ServiceFactory\Persistence\Mapping\Driver\ClassMapDriverFactory;
use Chubbyphp\Negotiation\AcceptNegotiatorInterface;
use Chubbyphp\Negotiation\ContentTypeNegotiatorInterface;
use Chubbyphp\Negotiation\Middleware\AcceptMiddleware;
use Chubbyphp\Negotiation\Middleware\ContentTypeMiddleware;
use Chubbyphp\Negotiation\ServiceFactory\AcceptMiddlewareFactory;
use Chubbyphp\Negotiation\ServiceFactory\AcceptNegotiatorFactory;
use Chubbyphp\Negotiation\ServiceFactory\ContentTypeMiddlewareFactory;
use Chubbyphp\Negotiation\ServiceFactory\ContentTypeNegotiatorFactory;
use Chubbyphp\Parsing\ParserInterface;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Tools\Console\ConnectionProvider;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Tools\Console\EntityManagerProvider;
use Doctrine\Persistence\Mapping\Driver\MappingDriver;
use Monolog\Level;
use Psr\Cache\CacheItemPoolInterface;
use Psr\Http\Message\ResponseFactoryInterface;
use Psr\Http\Message\StreamFactoryInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Mezzio\Twig\TwigRendererFactory;
use Mezzio\Template\TemplateRendererInterface;

$rootDir = \realpath(__DIR__ . '/..');
$cacheDir = $rootDir . '/var/cache/' . $env;
$logDir = $rootDir . '/var/log';

return [
    'chubbyphp' => [
        'cors' => [
            'allowCredentials' => false,
            'allowHeaders' => ['Accept', 'Content-Type'],
            'allowMethods' => ['DELETE', 'GET', 'POST', 'PUT'],
            'allowOrigins' => [],
            'exposeHeaders' => [],
            'maxAge' => 7200,
        ],
    ],
    'debug' => false,
    'dependencies' => [
        'aliases' => [
            EntityManager::class => EntityManagerInterface::class,
            TemplateRendererInterface::class => 'twig',
        ],
        'factories' => [
            AcceptMiddleware::class => AcceptMiddlewareFactory::class,
            AcceptNegotiatorInterface::class . 'supportedMediaTypes[]' => AcceptNegotiatorSupportedMediaTypesFactory::class,
            AcceptNegotiatorInterface::class => AcceptNegotiatorFactory::class,
            ApiExceptionMiddleware::class => ApiExceptionMiddlewareFactory::class,
            CacheItemPoolInterface::class => ApcuAdapterFactory::class,
            Command::class . '[]' => CommandsFactory::class,
            Connection::class => ConnectionFactory::class,
            ConnectionProvider::class => ContainerConnectionProviderFactory::class,
            ContentTypeMiddleware::class => ContentTypeMiddlewareFactory::class,
            ContentTypeNegotiatorInterface::class . 'supportedMediaTypes[]' => ContentTypeNegotiatorSupportedMediaTypesFactory::class,
            ContentTypeNegotiatorInterface::class => ContentTypeNegotiatorFactory::class,
            CorsMiddleware::class => CorsMiddlewareFactory::class,
            DecoderInterface::class => DecoderFactory::class,
            EncoderInterface::class => EncoderFactory::class,
            EntityManagerInterface::class => EntityManagerFactory::class,
            EntityManagerProvider::class => ContainerEntityManagerProviderFactory::class,
            ExceptionMiddleware::class => ExceptionMiddlewareFactory::class,
            LoggerInterface::class => LoggerFactory::class,
            MappingDriver::class => ClassMapDriverFactory::class,
            MiddlewareInterface::class . '[]' => MiddlewaresFactory::class,
            OpenapiRequestHandler::class => OpenapiRequestHandlerFactory::class,
            ParserInterface::class => ParserFactory::class,
            CreateRequestHandler::class => ArticleCreateRequestHandlerFactory::class,
            DeleteRequestHandler::class => ArticleDeleteRequestHandlerFactory::class,
            ListRequestHandler::class => ArticleListRequestHandlerFactory::class,
            ReadRequestHandler::class => ArticleReadRequestHandlerFactory::class,
            UpdateRequestHandler::class => ArticleUpdateRequestHandlerFactory::class,
            PetParsing::class => PetParsingFactory::class,
            ArticleParsing::class => ArticleParsingFactory::class,
            PetRepository::class => PetRepositoryFactory::class,
            ArticleRepository::class => ArticleRepositoryFactory::class,

            PingRequestHandler::class => PingRequestHandlerFactory::class,
            ResponseFactoryInterface::class => ResponseFactoryFactory::class,
            RouteMatcherInterface::class => RouteMatcherFactory::class,
            RouteMatcherMiddleware::class => RouteMatcherMiddlewareFactory::class,
            RoutesByNameInterface::class => RoutesByNameFactory::class,
            StreamFactoryInterface::class => StreamFactoryFactory::class,
            TypeDecoderInterface::class . '[]' => TypeDecodersFactory::class,
            TypeEncoderInterface::class . '[]' => TypeEncodersFactory::class,
            UrlGeneratorInterface::class => UrlGeneratorFactory::class,
            'twig' => TwigRendererFactory::class,
            'Twig\Environment' => function($container) {
                $paths = $container->get('config')['templates']['paths'];
                $loader = new \Twig\Loader\FilesystemLoader($paths);
                return new \Twig\Environment($loader);
            },
        ],
    ],
    'directories' => [
        'cache' => $cacheDir,
        'log' => $logDir,
        'db' => $rootDir . '/var',
    ],
    'doctrine' => [
        'cache' => [
            'apcu' => [
                'namespace' => 'doctrine',
            ],
        ],
        'dbal' => [
            'connection' => [
                'driver' => 'pdo_sqlite',
                'path' => $rootDir . '/database/database.sqlite',
            ],
        ],
        'driver' => [
            'classMap' => [
                Pet::class => PetMapping::class,
                Vaccination::class => VaccinationMapping::class,
                Article::class => ArticleMapping::class,
                Category::class => CategoryMapping::class,
            ],
        ],
        'orm' => [
            'configuration' => [
                'metadataDriverImpl' => MappingDriver::class,
                'proxyDir' => $cacheDir . '/doctrine/orm/proxies',
                'proxyNamespace' => 'DoctrineORMProxy',
                'metadataCache' => CacheItemPoolInterface::class,
            ],
        ],
    ],
    'fastroute' => [
        'cache' => $cacheDir . '/router-cache.php',
    ],
    'monolog' => [
        'name' => 'petstore',
        'path' => $logDir . '/' . $env . '.log',
        'level' => Level::Notice,
    ],
    'templates' => [
        'paths' => [
            'templates' => $rootDir . '/templates',
        ],
    ],
];
