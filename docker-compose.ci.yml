services:
  php:
    container_name: petstore-php
    hostname: petstore-php
    build:
      dockerfile: ./docker/production/php/Dockerfile
      context: ./
    environment:
      APP_ENV: prod
  swagger-ui:
    container_name: petstore-swagger-ui
    hostname: petstore-swagger-ui
    image: swaggerapi/swagger-ui
    environment:
      BASE_URL: /swagger
      URLS: '[ { url: "/openapi" } ]'
  nginx:
    container_name: petstore-nginx
    hostname: petstore-nginx
    image: nginx
    environment:
      SERVER_PORT: '443'
      PHP_FPM_SERVER_HOST: 'php'
      PHP_FPM_SERVER_PORT: '9000'
      SWAGGER_SERVER_HOST: 'swagger-ui'
      SWAGGER_SERVER_PORT: '8080'
    ports:
      - '443:443'
    volumes:
      - ./docker/development/nginx:/etc/nginx/templates
      - ${PWD}:/app:ro
    depends_on:
      - php
      - swagger-ui
