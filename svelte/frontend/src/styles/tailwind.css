@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles for demo pages (with Tailwind). Provide utility-like classes used by Svelte pages. */

:root {
  --bg: #0b1020;
  --bg-muted: #0f1428;
  --text: #e6eaf2;
  --text-muted: #b7c0d1;
  --primary: #2563eb; /* blue-600 */
  --primary-400: #60a5fa; /* blue-400 */
  --slate-700: #334155;
  --emerald-600: #059669;
  --radius: 0.5rem;
}

* {
  box-sizing: border-box;
}
html,
body {
  height: 100%;
}
html {
  color-scheme: dark;
}
body {
  margin: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    Segoe UI,
    Roboto,
    Ubuntu,
    Cantarell,
    Noto Sans,
    Helvetica Neue,
    Arial,
    "Apple Color Emoji",
    "Segoe UI Emoji";
  line-height: 1.5;
  background:
    radial-gradient(1200px 800px at 10% 10%, #121a33 0%, var(--bg) 55%),
    radial-gradient(1000px 700px at 90% 20%, #17203d 0%, transparent 60%),
    linear-gradient(180deg, var(--bg) 0%, #0a0f1c 100%);
  color: var(--text);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Utility to opt-in antialiasing like Tailwind's .antialiased */
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography helpers */
.font-sans {
  font-family: inherit;
}
.font-mono {
  font-family:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
}
.text-center {
  text-align: center;
}
.text-white {
  color: #ffffff;
}
.text-lg {
  font-size: 1.125rem;
}
.text-3xl {
  font-size: 1.875rem;
}
.font-bold {
  font-weight: 700;
}
.opacity-80 {
  opacity: 0.8;
}

/* Spacing utilities */
.p-6 {
  padding: 1.5rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}

/* Layout utilities */
.min-h-screen {
  min-height: 100vh;
}
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}

/* Buttons and basic components */
button,
.btn {
  border: 0;
  cursor: pointer;
  transition:
    transform 0.06s ease,
    filter 0.2s ease,
    background-color 0.2s ease;
}
.rounded {
  border-radius: var(--radius);
}
.bg-blue-600 {
  background-color: var(--primary);
}
.bg-emerald-600 {
  background-color: var(--emerald-600);
}
.bg-slate-700 {
  background-color: var(--slate-700);
}
.text-blue-400 {
  color: var(--primary-400);
}
.underline {
  text-decoration: underline;
}

button:hover {
  filter: brightness(1.08);
}
button:active {
  transform: translateY(1px) scale(0.99);
}

/* Links */
a {
  color: inherit;
  text-decoration: none;
}
a.underline {
  text-decoration: underline;
}
a.text-blue-400 {
  color: var(--primary-400);
}
a.text-blue-400:hover {
  filter: brightness(1.2);
}

/* Code blocks */
code {
  background-color: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.08);
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
}

/* Card container (not yet used but handy) */
.card {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.06),
    rgba(255, 255, 255, 0.03)
  );
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.25);
}

/* Selection */
::selection {
  background: rgba(96, 165, 250, 0.35);
  color: #fff;
}

/* Reduce motion users */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}
