<?php

declare(strict_types=1);

namespace App\Middleware;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

final class CacheMiddleware implements MiddlewareInterface
{
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $response = $handler->handle($request);

        // Cache HTMX responses for 60 seconds
        if ($request->getHeaderLine('HX-Request') === 'true') {
            return $response->withHeader('Cache-Control', 'public, max-age=60');
        }

        return $response;
    }
}
