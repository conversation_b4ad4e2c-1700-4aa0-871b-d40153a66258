<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.5/phpunit.xsd" backupGlobals="false" colors="true" executionOrder="random" processIsolation="false" resolveDependencies="true" stopOnFailure="false" cacheDirectory=".phpunit.cache" backupStaticProperties="false">
  <testsuites>
    <testsuite name="Unit">
      <directory>./tests/Unit</directory>
    </testsuite>
  </testsuites>
  <source>
    <include>
      <directory>./src</directory>
    </include>
    <exclude>
      <file>./src/console.php</file>
      <file>./src/container.php</file>
      <file>./src/web.php</file>
    </exclude>
  </source>
  <extensions>
    <bootstrap class="DG\BypassFinals\PHPUnitExtension">
      <parameter name="bypassFinal" value="true"/>
    </bootstrap>
  </extensions>
</phpunit>
