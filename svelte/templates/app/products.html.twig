{% extends "layout/default.html.twig" %}
{% import "partials/components.twig" as ui %}

{% block content %}

  {# Svelte ostrov pre košík #}
  <div
    data-component="AddToCart"
    data-props='{"productId": "{{ product.id }}"}'
    class="cart-island"
  >
    {# Fallback #}
    <button class="px-4 py-2 bg-blue-500 text-white rounded">
      Pridať do košíka - {{ product.price }} €
    </button>
  </div>

  {# HTMX live search #}
  <div class="mb-6">
    <input
      type="text"
      placeholder="Hľadať produkty..."
      class="w-full p-3 border rounded-lg text-gray-900"
      hx-get="/api/products/search"
      hx-trigger="keyup changed delay:500ms"
      hx-target="#search-results"
      hx-indicator=".search-indicator"
    >
  </div>

  {# Výsledky vyhľadávania #}
  <div id="search-results" class="grid grid-cols-1 md:grid-cols-3 gap-6">
    {% for product in products %}
      {{ include('partials/product_card.twig', { product: product }) }}
    {% endfor %}
  </div>

  {# Loading indicator #}
  <div class="search-indicator hidden">
    <div class="text-center py-4">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  </div>

{% endblock %}
