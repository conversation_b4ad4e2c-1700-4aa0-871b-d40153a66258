<?php

declare(strict_types=1);

namespace App\ServiceFactory\DecodeEncode;

use Chu<PERSON>php\DecodeEncode\Decoder\JsonTypeDecoder;
use <PERSON><PERSON><PERSON>p\DecodeEncode\Decoder\JsonxTypeDecoder;
use <PERSON><PERSON>php\DecodeEncode\Decoder\TypeDecoderInterface;
use Chu<PERSON>php\DecodeEncode\Decoder\UrlEncodedTypeDecoder;
use Chubbyphp\DecodeEncode\Decoder\YamlTypeDecoder;

final class TypeDecodersFactory
{
    /**
     * @return array<int, TypeDecoderInterface>
     */
    public function __invoke(): array
    {
        return [
            new JsonTypeDecoder(),
            new JsonxTypeDecoder(),
            new UrlEncodedTypeDecoder(),
            new YamlTypeDecoder(),
        ];
    }
}
