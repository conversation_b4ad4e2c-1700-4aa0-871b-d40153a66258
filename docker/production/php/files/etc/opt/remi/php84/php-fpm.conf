[global]
pid = /tmp/php-fpm.pid
daemonize = false
error_log = /proc/self/fd/2

[default]
user = php
group = php
listen = 0.0.0.0:9000
listen.owner = php
listen.group = php
pm = dynamic
pm.max_children = 10
pm.start_servers = 2
pm.min_spare_servers = 1
pm.max_spare_servers = 4
pm.max_requests = 400
request_terminate_timeout = 600
access.log = /proc/self/fd/2
clear_env = no
catch_workers_output = yes
decorate_workers_output = no
