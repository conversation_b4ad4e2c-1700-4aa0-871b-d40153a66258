RewriteEngine On
RewriteBase /

# Handle API routes
RewriteRule ^api/(.*)$ - [L]

# Handle static assets
RewriteRule ^build/(.*)$ - [L]

# Force correct MIME types for JavaScript and CSS files
<FilesMatch "\.js$">
  ForceType application/javascript
</FilesMatch>

<FilesMatch "\.css$">
  ForceType text/css
</FilesMatch>

# Alternative MIME type setting
AddType application/javascript .js
AddType text/css .css

# Route everything else to index.php for SPA
RewriteRule ^(.*)$ index.php [QSA,L]
