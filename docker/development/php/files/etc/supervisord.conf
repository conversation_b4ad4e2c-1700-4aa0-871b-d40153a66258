[unix_http_server]
file=/tmp/supervisor.sock

[supervisord]
logfile=/tmp/supervisord.log
loglevel=info
pidfile=/tmp/supervisord.pid
nodaemon=true
silent=true

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

[program:php-fpm]
command=/opt/remi/php84/root/usr/sbin/php-fpm -c /etc/opt/remi/php84/php-fpm.conf -F
stdout_logfile=/tmp/supervisord.log
stdout_syslog=true
stderr_logfile=/tmp/supervisord.log
stderr_syslog=true

