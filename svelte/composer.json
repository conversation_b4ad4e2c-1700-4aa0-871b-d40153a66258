{"name": "responsive-sk/svelte5boot", "description": "Mezzio + Svelte 5 demo application with Vite and Twig.", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["mezzio", "laminas", "svelte", "vite", "twig", "spa", "psr-15", "psr-7", "middleware"], "homepage": "https://mezzio.dev", "support": {"docs": "https://docs.mezzio.dev/mezzio/", "issues": "https://github.com/mezzio/mezzio-skeleton/issues", "source": "https://github.com/mezzio/mezzio-skeleton", "rss": "https://github.com/mezzio/mezzio-skeleton/releases.atom", "chat": "https://laminas.dev/chat", "forum": "https://discourse.laminas.dev"}, "config": {"sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "composer/package-versions-deprecated": true, "laminas/laminas-component-installer": true}, "platform": {"php": "8.3.99"}}, "extra": {"laminas": {"component-whitelist": ["mezzio/mezzio", "mezzio/mezzio-helpers", "mezzio/mezzio-router", "laminas/laminas-httphandlerrunner", "mezzio/mezzio-laminasrouter", "mezzio/mezzio-twigrenderer"]}}, "require": {"php": "~8.3.0 || ~8.4", "composer/package-versions-deprecated": "^1.11.99.5", "filp/whoops": "^2.15.4", "laminas/laminas-component-installer": "^2.6 || ^3.5", "laminas/laminas-config-aggregator": "^1.18", "laminas/laminas-diactoros": "^3.5.0", "laminas/laminas-servicemanager": "^3.22", "laminas/laminas-stdlib": "^3.20", "mezzio/mezzio": "^3.20.1", "mezzio/mezzio-csrf": "^1.10", "mezzio/mezzio-helpers": "^5.17", "mezzio/mezzio-session": "^1.16", "mezzio/mezzio-session-ext": "*", "mezzio/mezzio-twigrenderer": "^2.15", "sirix/mezzio-radixrouter": "^3.0", "sirix/sirix-config": "^1.0", "sirix/twig-vite-extension": "^1.0"}, "require-dev": {"laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-development-mode": "^3.13.0", "mezzio/mezzio-tooling": "^2.10.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.5.45", "psalm/plugin-phpunit": "^0.19.2", "roave/security-advisories": "dev-master", "vimeo/psalm": "^6.8.8"}, "conflict": {"amphp/dns": "<2.4.0", "amphp/socket": "<2.3.1", "php-di/php-di": "<7.0.9"}, "autoload": {"psr-4": {"App\\": "src/App/"}}, "autoload-dev": {"psr-4": {"AppTest\\": "test/AppTest/"}}, "scripts": {"post-create-project-cmd": ["@development-enable"], "post-install-cmd": "@clear-config-cache", "post-update-cmd": "@clear-config-cache", "development-disable": "laminas-development-mode disable", "development-enable": "laminas-development-mode enable", "development-status": "laminas-development-mode status", "mezzio": "laminas --ansi", "check": ["@cs-check", "@test"], "clear-config-cache": "php bin/clear-config-cache.php", "enable-codestandard": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin::run", "cs-check": "phpcs", "cs-fix": "phpcbf", "serve": ["Composer\\Config::disableProcessTimeout", "php -S 0.0.0.0:8080 -t public/"], "static-analysis": "psalm --stats", "static-analysis-update-baseline": "psalm --stats --update-baseline", "test": "phpunit --colors=always", "test-coverage": "phpunit --colors=always --coverage-clover clover.xml"}, "scripts-descriptions": {"clear-config-cache": "Clears merged config cache. Required for config changes to be applied.", "static-analysis": "Run static analysis tool Psalm.", "static-analysis-update-baseline": "Run static analysis tool Psalm and update baseline."}}