{% extends "layout/default.twig" %}

{% block content %}
<div class="container mx-auto p-4">
  <h1 class="text-3xl font-bold mb-6">Article Management</h1>

  <button
    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mb-4"
    hx-get="/api/articles/new"
    hx-target="#article-form-container"
    hx-swap="innerHTML"
  >
    Create New Article
  </button>

  <div id="article-form-container" class="mb-6"></div>

  <div
    id="article-list"
    hx-get="/api/articles"
    hx-trigger="load, articleUpdated from:body"
    hx-target="#article-list"
    hx-swap="innerHTML"
  >
    Loading articles...
  </div>
</div>

<script>
  document.body.addEventListener('articleSaved', function() {
    htmx.trigger('#article-list', 'articleUpdated');
    document.getElementById('article-form-container').innerHTML = '';
  });
</script>
{% endblock %}
