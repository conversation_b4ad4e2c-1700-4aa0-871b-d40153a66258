<?php

declare(strict_types=1);

namespace App\RequestHandler;

use <PERSON><PERSON><PERSON>\Template\TemplateRendererInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Laminas\Diactoros\Response\HtmlResponse;

final class HomePageRequestHandler implements RequestHandlerInterface
{
    private TemplateRendererInterface $template;

    public function __construct(TemplateRendererInterface $template)
    {
        $this->template = $template;
    }

    public function handle(ServerRequestInterface $request): ResponseInterface
    {
        $html = $this->template->render('app/home-page.twig', [
            'title' => 'Home Page',
        ]);

        return new HtmlResponse($html);
    }
}
