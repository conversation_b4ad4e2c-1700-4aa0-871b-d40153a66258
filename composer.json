{"name": "chubbyphp/petstore", "description": "A simple skeleton to build api's based on the chubbyphp-framework.", "keywords": ["chubbyphp", "framework", "skeleton"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "chubbyphp/chubbyphp-clean-directories": "^1.4", "chubbyphp/chubbyphp-cors": "^1.6", "chubbyphp/chubbyphp-decode-encode": "^1.2", "chubbyphp/chubbyphp-framework": "^5.2", "chubbyphp/chubbyphp-framework-router-fastroute": "^2.2", "chubbyphp/chubbyphp-http-exception": "^1.2", "chubbyphp/chubbyphp-laminas-config": "^1.4", "chubbyphp/chubbyphp-laminas-config-doctrine": "^3.0.2", "chubbyphp/chubbyphp-laminas-config-factory": "^1.4", "chubbyphp/chubbyphp-negotiation": "^2.2", "chubbyphp/chubbyphp-parsing": "^1.4.1", "doctrine/orm": "^3.5.2", "mezzio/mezzio-twigrenderer": "^2.17", "monolog/monolog": "^3.9", "ramsey/uuid": "^4.9", "slim/psr7": "^1.7.1", "symfony/console": "^7.3.2"}, "require-dev": {"chubbyphp/chubbyphp-dev-helper": "dev-master", "chubbyphp/chubbyphp-mock": "^2.0", "dg/bypass-finals": "^1.9", "infection/infection": "^0.31.1", "php-coveralls/php-coveralls": "^2.8", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^2.1.22", "phpunit/phpunit": "^11.5.32"}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "config": {"sort-packages": true, "allow-plugins": {"infection/extension-installer": true, "phpstan/extension-installer": true}}, "scripts": {"clean-directories": "bin/console clean-directories cache log", "database:create": "bin/console dbal:database:create --if-not-exists", "database:drop": "bin/console dbal:database:drop --if-exists --force", "database:schema:update": "bin/console orm:schema-tool:update --complete --force --dump-sql", "database:schema:validate": "bin/console orm:validate-schema", "fix:cs": "mkdir -p build && PHP_CS_FIXER_IGNORE_ENV=1 vendor/bin/php-cs-fixer fix --cache-file=build/phpcs.cache", "setup:dev": ["@database:schema:update --env=dev", "@database:schema:validate --env=dev", "@clean-directories --env=dev"], "setup:phpunit": ["@database:schema:update --env=phpunit", "@database:schema:validate --env=phpunit", "@clean-directories --env=phpunit"], "test": ["@test:lint", "@test:unit", "@test:infection", "@test:integration", "@test:static-analysis", "@test:cs"], "test:cs": "mkdir -p build && PHP_CS_FIXER_IGNORE_ENV=1 vendor/bin/php-cs-fixer fix --dry-run --stop-on-violation --cache-file=build/phpcs.cache", "test:infection": "vendor/bin/infection --threads=$(nproc) --min-msi=99 --verbose --coverage=build/phpunit", "test:integration": "vendor/bin/phpunit --configuration phpunit.integration.xml --cache-directory=build/phpunit/integration.cache", "test:lint": "mkdir -p build && find src tests -name '*.php' -print0 | xargs -0 -n1 -P$(nproc) php -l | tee build/phplint.log", "test:static-analysis": "mkdir -p build && bash -c 'vendor/bin/phpstan analyse src --no-progress --level=8 --error-format=junit | tee build/phpstan.junit.xml; if [ ${PIPESTATUS[0]} -ne \"0\" ]; then exit 1; fi'", "test:unit": "vendor/bin/phpunit --coverage-text --coverage-clover=build/phpunit/clover.xml --coverage-html=build/phpunit/coverage-html --coverage-xml=build/phpunit/coverage-xml --log-junit=build/phpunit/junit.xml --cache-directory=build/phpunit/unit.cache"}}