{# Alert komponent #}
{% macro alert(type, message) %}
  <div class="p-4 rounded-lg alert-{{ type }} {{ type == 'success' ? 'bg-green-100 text-green-800' : '' }} {{ type == 'error' ? 'bg-red-100 text-red-800' : '' }} {{ type == 'warning' ? 'bg-yellow-100 text-yellow-800' : '' }}">
    {{ message }}
  </div>
{% endmacro %}

{# Tlačidlo #}
{% macro button(text, variant = 'primary') %}
  <button class="px-4 py-2 rounded {{ variant == 'primary' ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-gray-300 text-gray-700 hover:bg-gray-400' }}">
    {{ text }}
  </button>
{% endmacro %}
