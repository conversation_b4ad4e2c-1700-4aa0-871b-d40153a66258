{"name": "svelte-inertia-mezzio-starter", "version": "0.1.0", "description": "The Svelte Inertia Mezzio Starter", "keywords": ["mezzio", "htmx", "svelte"], "license": "MIT", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^6.2.0", "@tailwindcss/postcss": "^4.1.13", "@tsconfig/svelte": "^5.0.5", "@types/node": "^24.5.2", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "concurrently": "^9.2.1", "eslint": "^9.35.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-svelte": "^3.12.3", "lucide-svelte": "^0.543.0", "mode-watcher": "^1.1.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.3.0", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "svelte": "^5.39.2", "svelte-check": "^4.3.1", "svelte-eslint-parser": "^1.3.2", "svelte-sonner": "^1.0.5", "tailwindcss": "^4.1.13", "typescript": "^5.9.2", "vite": "^7.1.6"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "dependencies": {"htmx": "^0.0.2"}}